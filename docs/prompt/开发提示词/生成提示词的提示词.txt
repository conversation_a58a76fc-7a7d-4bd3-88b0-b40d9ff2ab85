(中文!中文!中文!答复)基于提示词[prom_月度报告_s2_md2json.md]生成了结果[output_月度报告_s2.json],请修改提示词以便将数据最大程度使用图表呈现:
1,仅必要情况下,可以将单张表格数据拆分成多个图表呈现;
2,已经转换成图表的部分,也请推断是否恰当,可以进行重新定义修改;

(中文!中文!中文!答复)基于提示词[prom_月度报告_s2_md2json.md]生成了结果[output_月度报告_s2.json],请做如下修改:
1,观察["serial": "2.1.1"与"serial": "2.1.2"]["serial": "2.2.1"与"serial": "2.2.2"](或许还有其他),可以合并为一张MIXED样式的图表,成交套数用柱状,环比变化使用折线;
2,综合分析图表的拆分情况,强调相关数据尽量合并呈现,仅在数据相关性不大的情况下才拆分呈现;

请继续

(中文!中文!中文!答复)生成新的提示词文件,达到与[docs/prompt/7_价值评测/proms/prom_价值测评_s3_md2json.md]相同的效果:
1,markdown_content部分:参考文件[月度报告_s1_o1.md]里的内容作为
2,json_structure_definition部分:可以照搬(除非现有JSON结构无法满足界面要求,则需要你给出优化建议)
3,json_template部分:参考界面效果[月度报告_界面内容提取.md],特别要注意表格后对图表样式的说明,给出对应的JSON模板
4,system_prompt和user_prompt部分:参考[prom_价值测评_s3_md2json.md]相关部分,完成markdown_content+json_template=>json_report的转换


(中文!中文!中文!答复)请参考文档[docs/prompt/Markdown转换JSON/v4_progressive/step1_content_analysis.md]的分段结构,将提示词[docs/prompt/Markdown转换JSON/v5_基于JSON模板生成/ai_conversion_prompt.md]调整为[system_prompt\user_prompt\...]等分段格式